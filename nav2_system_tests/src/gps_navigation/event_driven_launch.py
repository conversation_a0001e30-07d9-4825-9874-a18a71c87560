#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
基于事件驱动的启动控制

使用Launch事件系统实现复杂的启动依赖关系。
"""

import os
from pathlib import Path

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import (AppendEnvironmentVariable, ExecuteProcess, IncludeLaunchDescription,
                            SetEnvironmentVariable, TimerAction,
                            RegisterEventHandler, EmitEvent, LogInfo, OpaqueFunction)
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.event_handlers import OnProcessStart, OnExecutionComplete
from launch_ros.actions import Node
from launch_ros.events.lifecycle import ChangeState
from lifecycle_msgs.msg import Transition
from nav2_common.launch import RewrittenYaml


def generate_launch_description():
    # 获取包目录和文件路径
    sim_dir = get_package_share_directory('nav2_minimal_tb3_sim')
    nav2_bringup_dir = get_package_share_directory('nav2_bringup')
    launch_dir = os.path.dirname(os.path.realpath(__file__))

    world_sdf_xacro = os.path.join(sim_dir, 'worlds', 'turtlebot3_house.sdf.xacro')
    robot_sdf = os.path.join(sim_dir, 'urdf', 'gz_waffle_gps.sdf.xacro')
    urdf = os.path.join(sim_dir, 'urdf', 'turtlebot3_waffle_gps.urdf')

    # 读取URDF
    with open(urdf, 'r') as infp:
        robot_description = infp.read()

    # 参数文件配置
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')
    indoor_ekf_params_file = os.path.join(launch_dir, 'indoor_ekf_params.yaml')
    dual_ekf_params_file = os.path.join(launch_dir, 'dual_ekf_navsat_params.yaml')

    configured_params = RewrittenYaml(source_file=params_file, root_key='', param_rewrites='', convert_types=True)
    configured_indoor_ekf_params = RewrittenYaml(source_file=indoor_ekf_params_file, root_key='', param_rewrites='', convert_types=True)

    # RViz配置文件路径
    rviz_config_file = os.path.join(launch_dir, 'rviz', 'gps_nav_view.rviz')

    # ============================================================================
    # 阶段1: 基础设施层
    # ============================================================================

    # Gazebo仿真器
    gazebo_server = ExecuteProcess(
        cmd=['gz', 'sim', '-r', '-s', world_sdf_xacro],
        output='screen',
        name='gazebo_server'
    )

    # 启动Gazebo仿真器客户端（GUI）
    gazebo_client = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('ros_gz_sim'),
                            'launch',
                            'gz_sim.launch.py')
        ),
        launch_arguments={'gz_args': ['-v4 -g ']}.items(),
    )

    # 机器人生成 (当Gazebo启动后)
    robot_spawn = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(sim_dir, 'launch', 'spawn_tb3_gps.launch.py')),
        launch_arguments={
            'use_sim_time': 'True',
            'robot_sdf': robot_sdf,
            'x_pose': '1.17',
            'y_pose': '-1.5',
            'z_pose': '0.01',
            'roll': '0.0',    # 初始横滚角
            'pitch': '0.0',   # 初始俯仰角
            'yaw': '0.0',     # 初始偏航角
        }.items(),
    )

    # 机器人状态发布器
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'use_sim_time': True, 'robot_description': robot_description}],
    )

    # ============================================================================
    # 阶段2: 传感器和定位基础
    # ============================================================================

    # Robot Localization包装器
    robot_localization_wrapper = Node(
        package='nav2_system_tests',
        executable='robot_localization_lifecycle_wrapper.py',
        name='robot_localization_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': dual_ekf_params_file},
            {'use_sim_time': True}
        ],
    )

    # 地图服务器
    map_server = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[configured_params, {'use_sim_time': True}],
        remappings=[('map', 'map'), ('map_metadata', 'map_metadata')],
    )

    # ============================================================================
    # 阶段3: 主要定位
    # ============================================================================

    # AMCL定位
    amcl = Node(
        package='nav2_amcl',
        executable='amcl',
        name='amcl',
        output='screen',
        parameters=[configured_params, {'use_sim_time': True}],
        remappings=[('scan', 'scan'), ('map', 'map'), ('map_metadata', 'map_metadata')],
    )

    # 室内EKF包装器
    indoor_ekf_wrapper = Node(
        package='nav2_system_tests',
        executable='indoor_ekf_lifecycle_wrapper.py',
        name='indoor_ekf_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': configured_indoor_ekf_params},
            {'use_sim_time': True}
        ],
    )

    # AMCL的生命周期管理器
    lifecycle_manager_amcl = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_amcl',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'node_names': ['amcl']}
        ]
    )

    # 室内EKF的生命周期管理器
    lifecycle_manager_indoor_ekf = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_indoor_ekf',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'bond_timeout': 0.0},  # 禁用bond机制（针对Python包装器）
            {'node_names': ['indoor_ekf_lifecycle_wrapper']}
        ]
    )

    # Robot Localization的生命周期管理器
    lifecycle_manager_robot_localization = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_robot_localization',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': True},  # 手动控制启动
            {'bond_timeout': 0.0},  # 禁用bond机制
            {'node_names': ['robot_localization_lifecycle_wrapper']}
        ]
    )

    # Map Server的生命周期管理器
    lifecycle_manager_map_server = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_map_server',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': True},   # 自动启动地图服务器
            {'node_names': ['map_server']}
        ]
    )

    # 传感器监控节点 - 持续检查传感器状态
    sensor_monitor = Node(
        package='nav2_system_tests',
        executable='sensor_monitor.py',
        name='sensor_monitor',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'required_sensors': ['scan', 'imu', 'gps', 'odom']},  # 必需的传感器
            {'check_frequency': 1.0},  # 降低检查频率，给更多时间让传感器稳定
            {'timeout_per_sensor': 5.0},  # 增加每个传感器的超时时间
        ]
    )

    # TF监控节点 - 持续检查TF转换链状态
    tf_monitor = Node(
        package='nav2_system_tests',
        executable='tf_monitor.py',
        name='tf_monitor',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'required_transforms': [
                'map->odom',
                'odom->base_link',
                'base_link->base_scan',
                'base_link->imu_link',
                'base_link->gps_link'
            ]},
            {'check_frequency': 1.0},  # TF检查频率，降低频率减少噪音
            {'transform_timeout': 5.0},  # TF转换超时时间，增加容忍度
            {'wait_for_transform_timeout': 2.0},  # 等待转换的超时时间
        ]
    )

    # 导航监控节点 - 持续检查导航系统组件状态
    nav_monitor = Node(
        package='nav2_system_tests',
        executable='nav_monitor.py',
        name='nav_monitor',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'required_nodes': [
                'controller_server',
                'planner_server',
                'smoother_server',
                'behavior_server',
                'bt_navigator',
                'waypoint_follower',
                'velocity_smoother',
                'collision_monitor'
            ]},
            {'check_frequency': 1.0},  # 导航检查频率，降低频率减少噪音
            {'service_timeout': 2.0},  # 服务超时时间
        ]
    )

    # ============================================================================
    # 阶段4: Nav2导航系统
    # ============================================================================

    # Nav2导航启动
    nav2_navigation = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(nav2_bringup_dir, 'launch', 'navigation_launch.py')  # Nav2导航启动文件
        ),
        launch_arguments={
            'namespace': '',  # 命名空间
            'use_sim_time': 'True',  # 使用仿真时间
            'params_file': configured_params,  # 配置参数文件
            'use_composition': 'False',  # 不使用组合节点
            'autostart': 'False',  # 不自动启动，由生命周期管理器控制
        }.items(),
    )

    # ============================================================================
    # 事件处理器 - 定义启动顺序
    # ============================================================================

    # 当Gazebo启动后，启动机器人相关组件
    start_robot_after_gazebo = RegisterEventHandler(
        OnProcessStart(
            target_action=gazebo_server,
            on_start=[
                LogInfo(msg="Gazebo started, launching robot components..."),
                TimerAction(
                    period=2.0,  # 等待2秒让Gazebo稳定
                    actions=[robot_spawn, robot_state_publisher, map_server, lifecycle_manager_map_server]
                )
            ]
        )
    )

    # 当机器人生成后，启动定位组件和导航组件（但不激活导航）
    start_localization_components_after_robot = RegisterEventHandler(
        OnExecutionComplete(
            target_action=robot_spawn,
            on_completion=[
                LogInfo(msg="Robot spawned, starting localization and navigation components..."),
                # 首先启动定位相关节点和生命周期管理器
                robot_localization_wrapper,
                amcl,
                indoor_ekf_wrapper,
                # 启动三个独立的生命周期管理器（地图服务器已在Gazebo启动时启动）
                lifecycle_manager_amcl,             # 管理AMCL
                lifecycle_manager_indoor_ekf,       # 管理室内EKF
                lifecycle_manager_robot_localization, # 管理Robot Localization
                # 启动传感器监控节点
                sensor_monitor,
                # 立即启动导航组件（但设置为不自动启动）
                nav2_navigation,
            ]
        )
    )

    # ============================================================================
    # 传感器监控事件处理器 - 基于传感器状态启动TF监控
    # ============================================================================

    # 当传感器监控节点检测到所有传感器就绪时，启动TF监控
    start_tf_monitor_when_sensors_ready = RegisterEventHandler(
        OnExecutionComplete(
            target_action=sensor_monitor,
            on_completion=[
                LogInfo(msg="All sensors are ready! Starting TF monitor..."),
                # 启动TF监控节点
                tf_monitor,
            ]
        )
    )

    # ============================================================================
    # TF监控事件处理器 - 基于TF状态配置生命周期管理器
    # ============================================================================

    # 当TF监控节点检测到所有TF转换就绪时，配置和激活导航系统
    configure_navigation_when_tf_ready = RegisterEventHandler(
        OnExecutionComplete(
            target_action=tf_monitor,
            on_completion=[
                LogInfo(msg="All TF transforms are ready! Configuring and activating navigation system..."),

                # 配置和激活导航系统
                ExecuteProcess(
                    cmd=['ros2', 'service', 'call', '/lifecycle_manager_navigation/manage_nodes',
                         'nav2_msgs/srv/ManageLifecycleNodes', '{command: 0}'],
                    output='screen',
                    name='configure_navigation'
                ),

                # 延迟启动导航监控器，等待导航系统完全启动
                TimerAction(
                    period=10.0,  # 等待5秒让导航系统完全启动
                    actions=[
                        LogInfo(msg="Starting navigation monitor..."),
                        nav_monitor,
                    ]
                ),


            ]
        )
    )

    # ============================================================================
    # 导航监控事件处理器 - 基于导航状态启动RViz2
    # ============================================================================

    # 当导航监控节点检测到所有导航组件就绪时，启动RViz2和环境检测节点
    start_rviz_when_nav_ready = RegisterEventHandler(
        OnExecutionComplete(
            target_action=nav_monitor,
            on_completion=[
                LogInfo(msg="All navigation components are ready! Starting RViz2 visualization and environment detector..."),

                # 启动RViz2可视化工具
                Node(
                    package='rviz2',
                    executable='rviz2',
                    name='rviz2',
                    arguments=['-d', rviz_config_file],
                    output='screen',
                    parameters=[{'use_sim_time': True}],
                    remappings=[
                        ('/tf', 'tf'),
                        ('/tf_static', 'tf_static'),
                    ],
                ),

                # 启动环境检测节点
                Node(
                    package='nav2_system_tests',
                    executable='environment_detector_node.py',
                    name='environment_detector',
                    output='screen',
                    parameters=[
                        {'use_sim_time': True},
                        {'check_interval': 1.0},
                        {'indoor_areas_config': os.path.join(launch_dir, 'config', 'indoor_areas_config.yaml')},
                        {'entrance_detection_radius': 2.0},
                        {'state_file_path': '/tmp/environment_state.json'},
                        {'position_change_threshold': 50.0},
                        {'force_check_on_startup': True},
                        {'debug_level': 'info'},
                        {'trajectory_buffer_size': 10},
                    ]
                ),
            ]
        )
    )

    # ============================================================================
    # 返回Launch描述
    # ============================================================================

    return LaunchDescription([
        # 设置日志环境变量
        SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),  # 启用缓冲流
        SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),  # 使用标准输出

        # 设置ROS2缓存清理环境变量
        SetEnvironmentVariable('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'),  # 使用CycloneDDS
        SetEnvironmentVariable('CYCLONEDDS_URI', '<Discovery><Peers><Peer address="localhost"/></Peers></Discovery>'),
        # 添加Gazebo仿真资源路径
        AppendEnvironmentVariable(
            'GZ_SIM_RESOURCE_PATH', os.path.join(sim_dir, 'models')  # 添加模型路径
        ),
        AppendEnvironmentVariable(
            'GZ_SIM_RESOURCE_PATH',
            str(Path(os.path.join(sim_dir)).parent.resolve()),  # 添加父目录路径
        ),
        # 添加turtlebot3_house模型路径
        AppendEnvironmentVariable(
            'GZ_SIM_RESOURCE_PATH',
            os.path.join(sim_dir, 'models', 'turtlebot3_house'),  # 添加house模型路径
        ),
        # 添加纹理路径
        AppendEnvironmentVariable(
            'GZ_SIM_RESOURCE_PATH',
            os.path.join(sim_dir, 'models', 'turtlebot3_house', 'cafe_table', 'materials', 'textures'),  # 添加纹理路径
        ),

        # 基础设施
        gazebo_server,
        gazebo_client,

        # 事件处理器
        start_robot_after_gazebo,
        start_localization_components_after_robot,

        # 传感器和TF监控事件处理器
        start_tf_monitor_when_sensors_ready,     # 传感器就绪时启动TF监控
        configure_navigation_when_tf_ready,      # TF就绪时配置导航
        start_rviz_when_nav_ready,               # 导航就绪时启动RViz2
    ])


if __name__ == '__main__':
    generate_launch_description()
